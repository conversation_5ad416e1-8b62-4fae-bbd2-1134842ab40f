using Aspire.Hosting;
using Microsoft.EntityFrameworkCore;

var builder = WebApplication.CreateBuilder(args);

// Add service defaults & Aspire client integrations.
builder.AddServiceDefaults();

// Add services to the container.
builder.Services.AddProblemDetails();
builder.Services.AddCors();
// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();

builder.AddNpgsqlDbContext<CounterDataDbContext>(connectionName: "posgres");

var app = builder.Build();

// Configure the HTTP request pipeline.
app.UseExceptionHandler();

if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
}

app.UseCors(x => x.AllowAnyOrigin());

app.MapPost("/bump", async (CounterDataDbContext context) =>
    {
        var counterData = await context.Counters.FindAsync(1);
        counterData.Counter++;
        await context.SaveChangesAsync();
        return true;
    })
.WithName("BumpCounter");

app.MapGet("/get_count", async (CounterDataDbContext context) =>
    {
        var counterData = await context.Counters.FindAsync(1);
        return counterData;
    })
.WithName("GetCounter");

app.MapDefaultEndpoints();

app.Run();



