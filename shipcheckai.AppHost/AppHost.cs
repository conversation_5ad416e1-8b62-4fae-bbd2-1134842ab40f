var builder = DistributedApplication.CreateBuilder(args);

var postgres = builder.AddPostgres("postgres");
var database = postgres.AddDatabase("testdb");

var apiService = builder.AddProject<Projects.shipcheckai_ApiService>("apiservice")
    .WithHttpHealthCheck("/health")
    .WithReference(database)
    .WaitFor(database);

builder.AddNpmApp("frontend", "../shipcheckai.Frontend")
    .WithExternalHttpEndpoints()
    // .WithHttpHealthCheck("/health")
    .WithReference(apiService)
    .WithEnvironment("BROWSER", "none")
    .WaitFor(apiService)
    .PublishAsDockerFile()
    .WithHttpEndpoint(env: "FRONTEND_PORT");

builder.AddProject<Projects.shipcheckai_Migrations>("migration")
    .WithReference(database)
    .WaitFor(database)
    .WithParentRelationship(apiService);

builder.Build().Run();